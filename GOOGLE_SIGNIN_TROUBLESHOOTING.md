# Google Sign-In Troubleshooting Guide

## Current Error: "GoogleSignInManager not initialized"

This error occurs because the Google Sign-In configuration is not properly set up. Here's how to fix it:

## Step 1: Google Cloud Console Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the **Google Sign-In API**
4. Go to **Credentials** → **Create Credentials** → **OAuth 2.0 Client IDs**

### For Android:
- Application type: **Android**
- Package name: `com.example.neorevv`
- SHA-1 certificate fingerprint: Run this command to get it:
  ```bash
  keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android
  ```

### For iOS:
- Application type: **iOS**
- Bundle ID: Your iOS bundle identifier (check in Xcode)

### For Web (if needed):
- Application type: **Web application**
- Add your domain to authorized origins

## Step 2: Download Configuration Files

### Android Configuration:
1. Download `google-services.json` from Google Cloud Console
2. Place it in `android/app/google-services.json`

### iOS Configuration:
1. Download `GoogleService-Info.plist` from Google Cloud Console
2. Add it to your iOS project in Xcode (`ios/Runner/`)

## Step 3: Update Environment Variables

Your `.env` file should contain:
```
GOOGLE_CLIENT_ID_WEB=your-web-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_ID_ANDROID=your-android-client-id.apps.googleusercontent.com
```

## Step 4: iOS URL Scheme Configuration

Update `ios/Runner/Info.plist` with your REVERSED_CLIENT_ID:

```xml
<key>CFBundleURLTypes</key>
<array>
    <dict>
        <key>CFBundleURLName</key>
        <string>REVERSED_CLIENT_ID</string>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>com.googleusercontent.apps.YOUR_REVERSED_CLIENT_ID</string>
        </array>
    </dict>
</array>
```

Replace `YOUR_REVERSED_CLIENT_ID` with the value from your `GoogleService-Info.plist`.

## Step 5: Test the Setup

1. Run `flutter clean && flutter pub get`
2. Test on a physical device (Google Sign-In doesn't work on emulators without Google Play Services)
3. Check the console logs for initialization messages

## Common Issues and Solutions

### Issue 1: "Sign in failed" error
**Solution**: Verify SHA-1 fingerprint matches exactly in Google Cloud Console

### Issue 2: "Network error"
**Solution**: Ensure device has internet and Google Play Services installed

### Issue 3: "Invalid client" error
**Solution**: Check package name matches exactly between app and Google Cloud Console

### Issue 4: No ID token received
**Solution**: Ensure 'openid' scope is included (already configured in your GoogleSignInManager)

## Debug Steps

1. **Check initialization logs**: Look for "GoogleSignInManager initialized successfully" message
2. **Verify environment variables**: Check if GOOGLE_CLIENT_ID values are loaded correctly
3. **Test on different devices**: Try both Android and iOS if possible
4. **Check network connectivity**: Ensure device can reach Google's servers

## Current Configuration Status

✅ Flutter dependencies installed
✅ GoogleSignInManager implemented
✅ Environment variables configured
✅ Android build.gradle updated
✅ iOS Info.plist prepared
❌ google-services.json missing (Android)
❌ GoogleService-Info.plist missing (iOS)
❌ iOS URL scheme not configured

## Next Steps

1. Complete Google Cloud Console setup
2. Download and add configuration files
3. Update iOS URL scheme
4. Test on physical device

## Testing Commands

```bash
# Clean and rebuild
flutter clean
flutter pub get

# Run on device
flutter run

# Check for Google Play Services (Android)
adb shell pm list packages | grep google
```

## Support

If you continue to have issues:
1. Check the Flutter logs: `flutter logs`
2. Verify your Google Cloud Console configuration
3. Ensure you're testing on a physical device with Google Play Services
