# Google Sign-In Setup Guide

This guide will help you configure Google Sign-In for your Flutter app.

## Prerequisites

1. Create a project in [Google Cloud Console](https://console.cloud.google.com/)
2. Enable the Google Sign-In API
3. Create OAuth 2.0 credentials for your app

## Step 1: Google Cloud Console Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the **Google Sign-In API**
4. Go to **Credentials** → **Create Credentials** → **OAuth 2.0 Client IDs**

### For Android:
- Application type: **Android**
- Package name: `com.example.neorevv` (or your actual package name)
- SHA-1 certificate fingerprint: Get this by running:
  ```bash
  keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android
  ```

### For iOS:
- Application type: **iOS**
- Bundle ID: Your iOS bundle identifier

### For Web (if needed):
- Application type: **Web application**
- Add your domain to authorized origins

## Step 2: Android Configuration

### 2.1 Add google-services.json
1. Download the `google-services.json` file from Google Cloud Console
2. Place it in `android/app/google-services.json`

### 2.2 Update android/build.gradle.kts
Add the Google Services plugin:

```kotlin
plugins {
    id("com.google.gms.google-services") version "4.4.0" apply false
}
```

### 2.3 Update android/app/build.gradle.kts
Add the plugin and dependency:

```kotlin
plugins {
    id("com.android.application")
    id("kotlin-android")
    id("dev.flutter.flutter-gradle-plugin")
    id("com.google.gms.google-services") // Add this line
}

dependencies {
    implementation("com.google.android.gms:play-services-auth:20.7.0")
}
```

## Step 3: iOS Configuration

### 3.1 Add GoogleService-Info.plist
1. Download the `GoogleService-Info.plist` file from Google Cloud Console
2. Add it to your iOS project in Xcode (ios/Runner/)

### 3.2 Update ios/Runner/Info.plist
Add the URL scheme:

```xml
<key>CFBundleURLTypes</key>
<array>
    <dict>
        <key>CFBundleURLName</key>
        <string>REVERSED_CLIENT_ID</string>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>YOUR_REVERSED_CLIENT_ID</string>
        </array>
    </dict>
</array>
```

Replace `YOUR_REVERSED_CLIENT_ID` with the value from your `GoogleService-Info.plist` file.

## Step 4: Initialize Google Sign-In

The Google Sign-In service is already implemented in the app. You can optionally configure it with specific parameters by modifying the initialization in `lib/src/presentation/screens/auth/login_screen.dart`:

```dart
GoogleSignInService.instance.initialize(
  scopes: ['email', 'profile'],
  // hostedDomain: 'your-domain.com', // Optional: restrict to specific domain
);
```

## Step 5: Test the Implementation

1. Run the app on a physical device (Google Sign-In doesn't work on emulators without Google Play Services)
2. Tap the "Sign in with Google" button
3. Complete the Google Sign-In flow
4. The app should receive the ID token and authenticate with your backend

## Troubleshooting

### Common Issues:

1. **"Sign in failed" error**: Check that your SHA-1 fingerprint is correctly configured in Google Cloud Console
2. **"Network error"**: Ensure your device has internet connectivity and Google Play Services
3. **"Invalid client"**: Verify that your package name and SHA-1 fingerprint match exactly

### Debug Steps:

1. Check the Android logs: `flutter logs`
2. Verify your `google-services.json` is in the correct location
3. Ensure your app's package name matches the one in Google Cloud Console
4. Test on a physical device with Google Play Services

## Security Notes

- Never commit your `google-services.json` or `GoogleService-Info.plist` files to version control if they contain sensitive information
- Use different OAuth clients for debug and release builds
- Implement proper token validation on your backend server

## Backend Integration

The app sends the Google ID token to your backend endpoint `/v1/auth/providers/google/signin`. Your backend should:

1. Verify the ID token with Google's servers
2. Extract user information (email, name, etc.)
3. Create or authenticate the user in your system
4. Return the same response format as your regular login endpoint (JWT + refresh token)
