import '../../core/network/dio_client.dart';
import '../../core/services/api_error_handler.dart';
import '/src/core/config/app_strings.dart';
import '/src/domain/models/login.dart';
import '../../core/network/api_config.dart' show APIConfig;
import '../../domain/repository/auth_repository.dart';
import '../../core/services/exceptions.dart';
import '../../core/network/api_config.dart';
import 'package:dio/dio.dart';

class AuthRepositoryImpl extends AuthRepository {
  AuthRepositoryImpl();

  static final String baseUrl = APIConfig.baseUrl;
  static const String loginUrl = APIConfig.login;
  static const String googleLoginUrl = APIConfig.googleLogin;

  @override
  Future<dynamic> login(Map<String, dynamic> payload) async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.post(loginUrl, data: payload);

      if (response.statusCode == 200) {
        return LoginModel.fromJson(response.data);
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, loginFailed);
    } catch (e) {
      throw ApiException(message: e.toString(), statusCode: 500);
    }
  }

  @override
  Future<dynamic> googleLogin(String idToken) async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.post(
        googleLoginUrl,
        data: {'idToken': idToken},
      );

      if (response.statusCode == 200) {
        return LoginModel.fromJson(response.data);
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, loginFailed);
    } catch (e) {
      throw ApiException(message: e.toString(), statusCode: 500);
    }
  }
}
