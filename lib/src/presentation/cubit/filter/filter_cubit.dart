import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:neorevv/src/domain/repository/broker_repository.dart';

import '../../../core/services/exceptions.dart';
import '../../../domain/models/filter_model.dart';
import '../../../domain/repository/agent_repository.dart';

part 'filter_state.dart';

class FilterCubit extends Cubit<FilterState> {
  final AgentRepository _agentRepository;
  final BrokerRepository _brokerRepository;

  FilterCubit(this._agentRepository, this._brokerRepository)
    : super(FilterInitial());

  List<FilterModel>? _agentFilterData;
  List<FilterModel>? _brokerFilterData;

  /// Get agent filter data list
  Future<void> getAgentFilterData() async {
    emit(FilterLoading());
    try {
      _agentFilterData = await _agentRepository.getAgentFilterData();
      emit(
        FilterLoaded(
          filterData: _agentFilterData,
          brokerFilterData: _brokerFilterData,
        ),
      );
    } on ApiException catch (e) {
      emit(FilterError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      emit(
        FilterError(message: 'An unexpected error occurred: \\${e.toString()}'),
      );
    }
  }

  /// Get broker filter data list
  Future<void> getBrokerFilterData() async {
    emit(FilterLoading());
    try {
      _brokerFilterData = await _brokerRepository.getBrokerFilterData();
      emit(
        FilterLoaded(
          filterData: _agentFilterData,
          brokerFilterData: _brokerFilterData,
        ),
      );
    } on ApiException catch (e) {
      emit(FilterError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      emit(
        FilterError(message: 'An unexpected error occurred: \\${e.toString()}'),
      );
    }
  }
}
