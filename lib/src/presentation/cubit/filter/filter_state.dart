part of 'filter_cubit.dart';

@immutable
sealed class FilterState {}

final class FilterInitial extends FilterState {}

final class FilterLoading extends FilterState {}

final class FilterLoaded extends FilterState {
  final List<FilterModel>? filterData;
  final List<FilterModel>? brokerFilterData;
  FilterLoaded({this.filterData, this.brokerFilterData});
}

final class FilterError extends FilterState {
  final String message;
  final int? statusCode;
  FilterError({required this.message, this.statusCode});
}
