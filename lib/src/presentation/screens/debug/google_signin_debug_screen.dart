import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import '../../../core/services/google_signin_manager.dart';

class GoogleSignInDebugScreen extends StatefulWidget {
  const GoogleSignInDebugScreen({super.key});

  @override
  State<GoogleSignInDebugScreen> createState() => _GoogleSignInDebugScreenState();
}

class _GoogleSignInDebugScreenState extends State<GoogleSignInDebugScreen> {
  String _status = 'Not initialized';
  String _logs = '';

  @override
  void initState() {
    super.initState();
    _checkStatus();
  }

  void _addLog(String message) {
    setState(() {
      _logs += '${DateTime.now().toString().substring(11, 19)}: $message\n';
    });
  }

  void _checkStatus() {
    final manager = GoogleSignInManager.instance;
    setState(() {
      if (manager.initializationFailed) {
        _status = 'Failed: ${manager.initializationError}';
      } else if (manager.isInitialized) {
        _status = 'Initialized successfully';
      } else {
        _status = 'Not initialized';
      }
    });
  }

  Future<void> _testInitialization() async {
    _addLog('Testing initialization...');
    try {
      await GoogleSignInManager.instance.initialize();
      _addLog('Initialization completed');
      _checkStatus();
    } catch (e) {
      _addLog('Initialization error: $e');
      _checkStatus();
    }
  }

  Future<void> _testSignIn() async {
    _addLog('Testing sign in...');
    try {
      if (!GoogleSignInManager.instance.isInitialized) {
        _addLog('Manager not initialized, initializing first...');
        await GoogleSignInManager.instance.initialize();
      }

      final account = await GoogleSignInManager.instance.googleSignIn.signIn();
      if (account != null) {
        _addLog('Sign in successful: ${account.email}');
        final auth = await account.authentication;
        _addLog('ID Token available: ${auth.idToken != null}');
        _addLog('Access Token available: ${auth.accessToken != null}');
      } else {
        _addLog('Sign in cancelled by user');
      }
    } catch (e) {
      _addLog('Sign in error: $e');
    }
  }

  void _checkEnvironmentVariables() {
    _addLog('Checking environment variables...');
    final webClientId = dotenv.env['GOOGLE_CLIENT_ID_WEB'];
    final androidClientId = dotenv.env['GOOGLE_CLIENT_ID_ANDROID'];
    
    _addLog('Web Client ID: ${webClientId != null ? 'Set' : 'Not set'}');
    _addLog('Android Client ID: ${androidClientId != null ? 'Set' : 'Not set'}');
    
    if (webClientId != null) {
      _addLog('Web Client ID: ${webClientId.substring(0, 20)}...');
    }
    if (androidClientId != null) {
      _addLog('Android Client ID: ${androidClientId.substring(0, 20)}...');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Google Sign-In Debug'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Status: $_status',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        ElevatedButton(
                          onPressed: _checkStatus,
                          child: const Text('Check Status'),
                        ),
                        ElevatedButton(
                          onPressed: _testInitialization,
                          child: const Text('Test Init'),
                        ),
                        ElevatedButton(
                          onPressed: _testSignIn,
                          child: const Text('Test Sign In'),
                        ),
                        ElevatedButton(
                          onPressed: _checkEnvironmentVariables,
                          child: const Text('Check Env'),
                        ),
                        ElevatedButton(
                          onPressed: () {
                            setState(() {
                              _logs = '';
                            });
                          },
                          child: const Text('Clear Logs'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Debug Logs:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.grey[50],
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _logs.isEmpty ? 'No logs yet...' : _logs,
                    style: const TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
