import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:neorevv/src/presentation/cubit/filter/filter_cubit.dart';
import '../../../domain/models/user.dart';
import '/src/presentation/shared/components/tables/action_button_eye.dart';
import '/src/core/config/app_strings.dart';
import '/src/core/config/constants.dart';
import '/src/core/theme/app_fonts.dart';
import '/src/core/theme/app_theme.dart';
import '/src/core/utils/date_formatter.dart';
import '/src/presentation/cubit/user/user_cubit.dart';
import '/src/presentation/cubit/agent/agent_cubit.dart';
import '/src/domain/models/agent_model.dart';
import '../../../domain/models/filter_model.dart';

import '/src/presentation/shared/components/tables/CustomDataTableWidget.dart';

class AgentsListScreen extends HookWidget {
  // Fetch filter data on screen load
  const AgentsListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Fetch filter data on mount
    useEffect(() {
      Future.microtask(() {
        _fetchFilterAgents(context);
        _fetchFilterBrokers(context);
      });
      return null;
    }, []);
    final user = context.watch<UserCubit>().state.user;
    final sortColumn = useState<String>('');
    final sortAscending = useState<bool>(true);
    final ValueNotifier<String?> searchString = useState(null);
    final ValueNotifier<DateTime?> selectedDate = useState(null);
    final pageCount = useState(0);
    final currentpage = useState(0);
    final totalElements = useState(0);

    // Add this useEffect to fetch initial data
    useEffect(() {
      Future.microtask(() {
        if (context.mounted && user != null) {
          _fetchAgents(
            context,
            user,
            selectedDate: selectedDate.value,
            page: 0,
            searchString: searchString.value,
          );
        }
      });
      return null;
    }, [user?.userId]);

    // Use predefined headers from app_strings.dart (excluding agentStatus for now)
    final List<String> formattedHeaders = [
      agentName,
      agentContact,
      agentEmail,
      agentJoinDate,
      agentState,
      agentCity,
      agentLevel,
      agentRelatedBrokerage,
      agentRefferedBy,
      agentDirectRecruits,
      agentTotalSales,
      agentTotalRevenue,
      agentCommission,
      agentStatus,
      // agentStatus, // Commented out as it's not in API yet
    ];
    // Get user from UserCubit
    final userCubit = context.read<UserCubit>().state;

    if (userCubit is! UserLoaded) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              userNotFound,
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(pleaseLoginToAccessAgentData),
          ],
        ),
      );
    }

    return BlocConsumer<FilterCubit, FilterState>(
      listener: (context, filterState) {
        // Optional: handle side effects here
      },
      builder: (context, filterState) {
        List<FilterModel>? filterData;
        List<FilterModel>? brokerFilterData;
        if (filterState is FilterLoaded) {
          filterData = filterState.filterData;
          brokerFilterData = filterState.brokerFilterData;
        }
        return BlocConsumer<AgentCubit, AgentState>(
          listener: (BuildContext context, AgentState state) {
            if (state is AgentLoaded) {
              pageCount.value = state.totalPages;
              totalElements.value = state.totalCount;
            }
          },
          builder: (context, state) {
            List<AgentModel> agentData = [];
            String? errorMessage;
            if (state is AgentLoaded) {
              agentData = state.agents;
            } else if (state is AgentError) {
              errorMessage = state.message;
            }

            List<AgentModel> getSortedAgents() {
              if (agentData.isEmpty) return <AgentModel>[];
              final sorted = List<AgentModel>.from(agentData);
              if (sortColumn.value.isNotEmpty) {
                sorted.sort((a, b) {
                  dynamic aValue, bValue;
                  switch (sortColumn.value) {
                    case agentName:
                      aValue = a.fullName;
                      bValue = b.fullName;
                      break;
                    case agentContact:
                      aValue = a.phone;
                      bValue = b.phone;
                      break;
                    case agentEmail:
                      aValue = a.email;
                      bValue = b.email;
                      break;
                    case agentJoinDate:
                      aValue = a.joiningDate;
                      bValue = b.joiningDate;
                      break;
                    case agentState:
                      aValue = a.state;
                      bValue = b.state;
                      break;
                    case agentCity:
                      aValue = a.city;
                      bValue = b.city;
                      break;
                    case agentLevel:
                      aValue = a.level;
                      bValue = b.level;
                      break;
                    case agentRelatedBrokerage:
                      aValue = a.associatedBroker;
                      bValue = b.associatedBroker;
                      break;
                    case agentRefferedBy:
                      aValue = a.referredBy;
                      bValue = b.referredBy;
                      break;
                    case agentDirectRecruits:
                      aValue = a.totalDownlineAgents;
                      bValue = b.totalDownlineAgents;
                      break;
                    case agentTotalSales:
                      aValue = a.totalSales;
                      bValue = b.totalSales;
                      break;
                    case agentTotalRevenue:
                      aValue = a.totalRevenue;
                      bValue = b.totalRevenue;
                      break;
                    case agentCommission:
                      aValue = a.commission;
                      bValue = b.commission;
                      break;
                    case agentStatus:
                      aValue = a.status;
                      bValue = b.status;
                      break;
                    default:
                      aValue = '';
                      bValue = '';
                  }
                  final comparison = aValue is num && bValue is num
                      ? aValue.compareTo(bValue)
                      : aValue is DateTime && bValue is DateTime
                      ? aValue.compareTo(bValue)
                      : aValue.toString().compareTo(bValue.toString());
                  return sortAscending.value ? comparison : -comparison;
                });
              }
              return sorted;
            }

            void handleSort(String columnName, bool ascending) {
              sortColumn.value = columnName;
              sortAscending.value = ascending;
            }

            final sortedAgents = getSortedAgents();

            if (errorMessage != null) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      errorMessage.contains(unauthorizedStatus) ||
                              errorMessage.contains(unauthorizedText)
                          ? Icons.lock_outline
                          : Icons.error_outline,
                      size: 64,
                      color: Colors.red,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      errorMessage.contains(unauthorizedStatus) ||
                              errorMessage.contains(unauthorizedText)
                          ? authenticationRequired
                          : errorLoadingData,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      errorMessage.contains(unauthorizedStatus) ||
                              errorMessage.contains(unauthorizedText)
                          ? pleaseLoginToAccessAgentData
                          : '$errorPrefix$errorMessage',
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        _fetchAgents(
                          context,
                          user,
                          selectedDate: selectedDate.value,
                          page: currentpage.value,
                          searchString: searchString.value,
                        );
                      },
                      child: const Text(retry),
                    ),
                  ],
                ),
              );
            }
            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Flexible(
                  child: CustomDataTableWidget<AgentModel>(
                    data: sortedAgents,
                    title: agents,
                    titleIcon: "$iconAssetpath/user.png",
                    searchHint: searchAgent,
                    searchFn: (agent) =>
                        agent.fullName +
                        agent.phone +
                        agent.email +
                        agent.state +
                        agent.city +
                        agent.level +
                        AppDateFormatter.formatJoiningDate(agent.joiningDate) +
                        agent.referredBy +
                        agent.totalDownlineAgents.toString() +
                        agent.totalSales.toString() +
                        agent.commission.toString() +
                        agent.status.toString(),

                    // Dynamic filtering system
                    filterColumnNames: [
                      agentName, // name
                      agentRelatedBrokerage,
                      agentStatus, // status // dynamic filter from API
                    ],
                    filterValueExtractors: {
                      agentName: (agent) => agent.fullName,
                      agentRelatedBrokerage: (agent) => agent.associatedBroker,
                      agentStatus: (agent) => agent.status.toString(),
                    },
                    // Use filterData and brokerFilterData for dropdown options in the filter section, mapping id to value
                    filterOptions: {
                      if (filterData != null) agentName: filterData,
                      if (brokerFilterData != null)
                        agentRelatedBrokerage: brokerFilterData,
                    },
                    columnNames: formattedHeaders,
                    cellBuilders: [
                      (agent) => agent.fullName, // name
                      (agent) => agent.phone, // contact
                      (agent) => agent.email, // email
                      (agent) => AppDateFormatter.formatDateMMddyyyy(
                        agent.joiningDate,
                      ), // joinDate
                      (agent) => agent.state, // state
                      (agent) => agent.city, // city
                      (agent) => agent.level, // level
                      (agent) => agent.associatedBroker, //associate broker
                      (agent) => agent.referredBy, // referredBy - MISSING!
                      (agent) => agent.totalDownlineAgents
                          .toString(), // totalAgents - MISSING!
                      (agent) => agent.totalSales.toString(), // totalSales
                      (agent) =>
                          '$currencySymbol${agent.totalRevenue.toStringAsFixed(2)}', // total revenue
                      (agent) =>
                          '$currencySymbol${agent.commission.toStringAsFixed(2)}', // commission
                      (agent) => agent.status
                          ? 'Active'
                          : 'Inactive', // status - MISSING!
                    ],
                    iconCellBuilders: [
                      (agent) => TableCellData(
                        text: agent.fullName,
                        leftIconAsset: "$iconAssetpath/agent_round.png",
                        iconSize: 30,
                      ),
                      null, // contact
                      null, // email
                      null, // joinDate
                      null, // state
                      null, // city
                      null, // level
                      null, // associatedBroker
                      null, // referredBy
                      null, // Direct recruites
                      null,
                      null, //total revenue
                      null, // totalSales
                      null, // commission
                      null, // status
                    ],
                    useIconBuilders: [
                      true, // name - use icon
                      false, // contact
                      false, // email
                      false, // joinDate
                      false, // state
                      false, // city
                      false, // level
                      false, //associatedBroker
                      false, // referredBy
                      false, // totalAgents
                      false,
                      false, // totalSales
                      false, // commission
                      false, // status
                    ],
                    // Widget builders for styled cells
                    widgetCellBuilders: [
                      null, // name - use text
                      null, // contact - use text
                      null, // email - use text
                      null, // joinDate - use text
                      null, // state - use text
                      null, // city - use text
                      null, // level - use text
                      null, //
                      null,
                      null,
                      null, //
                      null,
                      null,
                      (context, agent) => Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: agent.status
                              ? AppTheme
                                    .agentStatusActiveBg // Light green background
                              : AppTheme
                                    .agentStatusInactiveBg, // Light red/pink background
                          borderRadius: BorderRadius.circular(
                            20,
                          ), // More rounded for oval shape
                        ),
                        child: Text(
                          agent.status ? active : inactive,
                          style: AppFonts.boldTextStyle(
                            12,
                            color: agent.status
                                ? AppTheme
                                      .white // Darker green text
                                : AppTheme.black,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                    // Boolean flags to indicate which columns use widget builders
                    useWidgetBuilders: [
                      false, // name
                      false, // contact
                      false, // email
                      false, // joinDate
                      false, // state
                      false, // city
                      false, // level
                      false, // commission
                      false, //associatedBroker
                      false, // referredBy
                      false, // totalAgents
                      false, // totalSales
                      false, // commission
                      true, // status
                    ],
                    actionBuilders: [
                      (context, agent) => ActionButtonEye(
                        onPressed: () => _onAgentAction(context, agent),
                        isCompact: true,
                        isMobile: false,
                      ),
                    ],
                    mobileCardBuilder: (context, agent) =>
                        _buildMobileAgentCard(agent, context),
                    onSort: handleSort,
                    emptyStateMessage: noDataAvailable,
                    pageCount: pageCount.value,
                    isLoading: state is AgentLoading,
                    totalElements: totalElements.value,
                    onDateFilterChanged: (value) async {
                      selectedDate.value = value;
                      await _fetchAgents(
                        context,
                        user,
                        selectedDate: value,
                        page: currentpage.value,
                        searchString: searchString.value,
                      );
                    },
                    handleTableSearch: (value) async {
                      searchString.value = value;
                      await _fetchAgents(
                        context,
                        user,
                        selectedDate: selectedDate.value,
                        page: currentpage.value,
                        searchString: value,
                      );
                    },
                    handlePagination: (page) async {
                      currentpage.value = page;
                      await _fetchAgents(
                        context,
                        user,
                        selectedDate: selectedDate.value,
                        searchString: searchString.value,
                        page: page,
                      );
                    },
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  _fetchAgents(
    BuildContext context,
    User? user, {
    required DateTime? selectedDate,
    required int page,
    required String? searchString,
  }) {
    if (context.mounted) {
      String? formattedDate = selectedDate != null
          ? AppDateFormatter.formatDateMMddyyyy(selectedDate)
          : null;

      final payload = {
        "page": page > 0 ? page - 1 : 0,
        "size": 10,
        "sortBy": "id",
        "sortDirection": "ASC",
        "searchString": searchString,
        "joiningDate": formattedDate,
        "userId": user?.userId,
      };

      context.read<AgentCubit>().getAgents(requestBody: payload);
    }
  }

  //fetch agent name filter data for listing.
  _fetchFilterAgents(BuildContext context) {
    if (context.mounted) {
      context.read<FilterCubit>().getAgentFilterData();
    }
  }

  //fetch broker filter data for listing.
  _fetchFilterBrokers(BuildContext context) {
    if (context.mounted) {
      context.read<FilterCubit>().getBrokerFilterData();
    }
  }

  void _onAgentAction(BuildContext context, AgentModel agent) {
    // Navigate to agent detail or show action
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('$actionClickedFor ${agent.fullName}')),
    );
  }

  Widget _buildMobileAgentCard(AgentModel agent, BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                agent.fullName,
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: agent.status
                      ? AppTheme
                            .agentStatusActiveBg // Light green background
                      : AppTheme
                            .agentStatusInactiveBg, // Light red/pink background
                  borderRadius: BorderRadius.circular(
                    20,
                  ), // More rounded for oval shape
                ),
                child: Text(
                  agent.status ? active : inactive,
                  style: AppFonts.normalTextStyle(
                    12,
                    color: agent.level == agentRoleValue
                        ? AppTheme
                              .agentStatusActiveText // Darker green text
                        : AppTheme.agentStatusInactiveText,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text('$agentContact: ${agent.phone}'),
          Text('$agentEmail: ${agent.email}'),
          Text(
            '$agentJoinDate: ${AppDateFormatter.formatDateMMddyyyy(agent.joiningDate)}',
          ),
          Text('$agentState: ${agent.state}'),
          Text('$agentCity: ${agent.city}'),
          Text('$agentLevel: ${agent.level}'),
          Text('$agentRelatedBrokerage: ${agent.associatedBroker}'),
          Text('$agentRefferedBy: ${agent.referredBy}'),
          Text('$agentDirectRecruits: ${agent.totalDownlineAgents}'),
          Text('$agentTotalSales: ${agent.totalSales}'),
          Text(
            '$agentTotalRevenue: $currencySymbol${agent.totalRevenue.toStringAsFixed(2)}',
          ),
          Text(
            '$agentCommission: $currencySymbol${agent.commission.toStringAsFixed(2)}',
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: ActionButtonEye(
              onPressed: () => _onAgentAction(context, agent),
              isMobile: true,
            ),
          ),
        ],
      ),
    );
  }
}
