import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

import 'package:google_sign_in/google_sign_in.dart';

class GoogleSignInManager {
  static GoogleSignInManager? _instance;
  static GoogleSignInManager get instance =>
      _instance ??= GoogleSignInManager._();

  GoogleSignInManager._();

  GoogleSignIn? _googleSignIn;
  bool _isInitialized = false;
  bool _initializationFailed = false;
  String? _initializationError;

  GoogleSignIn get googleSignIn {
    if (_initializationFailed) {
      throw StateError(
        'GoogleSignInManager initialization failed: $_initializationError',
      );
    }
    if (_googleSignIn == null) {
      throw StateError(
        'GoogleSignInManager not initialized. Call initialize() first.',
      );
    }
    return _googleSignIn!;
  }

  bool get isInitialized => _isInitialized && !_initializationFailed;
  bool get initializationFailed => _initializationFailed;
  String? get initializationError => _initializationError;

  Future<void> initialize() async {
    if (_isInitialized || _initializationFailed) return;

    try {
      if (kIsWeb) {
        _googleSignIn = GoogleSignIn(
          clientId: dotenv.env['GOOGLE_CLIENT_ID_WEB'],
          scopes: [
            'openid',
            'email',
            'profile',
          ], // openid must be first for idToken
        );
      } else {
        // Mobile configuration - also needs openid for idToken
        try {
          _googleSignIn = GoogleSignIn(
            scopes: [
              'openid',
              'email',
              'profile',
            ], // openid must be first for idToken
          );
        } catch (e) {
          print('Trying with serverClientId due to error: $e');
          _googleSignIn = GoogleSignIn(
            scopes: [
              'openid',
              'email',
              'profile',
            ], // openid must be first for idToken
            serverClientId: dotenv.env['GOOGLE_CLIENT_ID_ANDROID'],
          );
        }
      }

      _isInitialized = true;
      _initializationFailed = false;
      _initializationError = null;
      print(
        'GoogleSignInManager initialized successfully for ${kIsWeb ? 'web' : 'mobile'}',
      );
      if (kIsWeb) {
        print('Using Web Client ID: ${dotenv.env['GOOGLE_CLIENT_ID_WEB']}');
      } else {
        print(
          'Using Android Client ID: ${dotenv.env['GOOGLE_CLIENT_ID_ANDROID']}',
        );
        print('Package name should be: com.example.recallloop');
        print(
          'SHA-1 should be: 53:D3:7C:B7:17:F7:BF:EC:2A:2B:06:F4:E4:F5:3F:F1:6D:CF:B5:D1',
        );
      }
    } catch (error) {
      _initializationFailed = true;
      _initializationError = error.toString();
      _isInitialized = false;
      print('GoogleSignInManager initialization error: $error');
    }
  }

  void dispose() {
    _googleSignIn = null;
    _isInitialized = false;
    _initializationFailed = false;
    _initializationError = null;
  }
}
