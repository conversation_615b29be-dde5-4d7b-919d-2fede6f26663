import 'package:google_sign_in/google_sign_in.dart';
import '../services/exceptions.dart';

class GoogleSignInService {
  static GoogleSignInService? _instance;
  static GoogleSignInService get instance =>
      _instance ??= GoogleSignInService._();

  GoogleSignInService._();

  late final GoogleSignIn _googleSignIn;

  /// Initialize Google Sign-In with configuration
  void initialize({
    List<String> scopes = const ['email', 'profile'],
    String? hostedDomain,
    String? clientId,
  }) {
    _googleSignIn = GoogleSignIn(
      scopes: scopes,
      hostedDomain: hostedDomain,
      // For web, you might need to specify clientId
      // For mobile, it's configured in platform-specific files
    );
  }

  /// Sign in with Google and return the ID token
  Future<String?> signIn() async {
    try {
      // Attempt to sign in
      final GoogleSignInAccount? account = await _googleSignIn.signIn();

      if (account == null) {
        // User cancelled the sign-in
        return null;
      }

      // Get authentication details
      final GoogleSignInAuthentication authentication =
          await account.authentication;

      // Return the ID token
      return authentication.idToken;
    } catch (e) {
      throw ApiException(
        message: 'Google Sign-In failed: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  /// Sign out from Google
  Future<void> signOut() async {
    try {
      await _googleSignIn.signOut();
    } catch (e) {
      throw ApiException(
        message: 'Google Sign-Out failed: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  /// Check if user is currently signed in
  Future<bool> isSignedIn() async {
    try {
      return await _googleSignIn.isSignedIn();
    } catch (e) {
      return false;
    }
  }

  /// Get current signed-in account
  GoogleSignInAccount? get currentUser => _googleSignIn.currentUser;

  /// Silently sign in (if user was previously signed in)
  Future<String?> signInSilently() async {
    try {
      final GoogleSignInAccount? account = await _googleSignIn.signInSilently();

      if (account == null) {
        return null;
      }

      final GoogleSignInAuthentication authentication =
          await account.authentication;
      return authentication.idToken;
    } catch (e) {
      // Silent sign-in failed, user needs to sign in manually
      return null;
    }
  }

  /// Disconnect from Google (revokes access)
  Future<void> disconnect() async {
    try {
      await _googleSignIn.disconnect();
    } catch (e) {
      throw ApiException(
        message: 'Google disconnect failed: ${e.toString()}',
        statusCode: 500,
      );
    }
  }
}
